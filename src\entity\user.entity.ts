import { Table, Column, DataType, Model } from 'sequelize-typescript';
import * as bcrypt from 'bcryptjs';

export interface UserAttributes {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 昵称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 邮箱 */
  email?: string;
  /** 角色 */
  role: string;
  /** 是否激活 */
  isActive?: boolean;
}

/**
 * 用户表模型
 */
@Table({
  tableName: 'user',
  comment: '用户表',
})
export class User extends Model<UserAttributes> implements UserAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    unique: true,
    comment: '用户名',
  })
  username: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '加密密码',
    set(val: string) {
      // 检查是否已经是加密后的密码（bcrypt hash通常以$2开头且长度为60）
      if (val && val.length === 60 && val.startsWith('$2')) {
        this.setDataValue('password', val);
      } else {
        // 同步加密密码
        const hashedPassword = bcrypt.hashSync(val, 10);
        this.setDataValue('password', hashedPassword);
      }
    },
  })
  password: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '昵称',
  })
  nickname: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '头像',
  })
  avatar: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '邮箱',
  })
  email: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    defaultValue: 'admin',
    comment: '角色（管理员等）',
  })
  role: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用',
  })
  isActive: boolean;
}
