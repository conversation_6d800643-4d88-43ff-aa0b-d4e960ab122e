import { Provide, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import { JwtService } from '@midwayjs/jwt';
import { Op } from 'sequelize';
import * as bcrypt from 'bcryptjs';
import {
  CreateUserDTO,
  UpdateUserDTO,
  LoginDTO,
  LoginResponseDTO,
  UserResponseDTO,
  UserQueryDTO,
} from '../dto/user.dto';
import { PageResponseDTO } from '../dto/common.dto';
import { User } from '../entity/user.entity';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class UserService {
  @Inject()
  jwtService: JwtService;

  /**
   * 用户登录
   */
  async login(loginDto: LoginDTO): Promise<LoginResponseDTO> {
    const { username, password } = loginDto;

    // 查找用户
    const user = await User.findOne({
      where: { username },
    });

    if (!user) {
      throw new Error('用户名不存在');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new Error('用户名或密码错误');
    }

    // 检查用户是否激活
    if (!user.isActive) {
      throw new Error('用户已被禁用');
    }

    // 生成JWT Token
    const token = await this.jwtService.sign({
      userId: user.id,
      username: user.username,
      role: user.role,
    });

    return new LoginResponseDTO(token, user);
  }

  /**
   * 创建用户
   */
  async createUser(createUserDto: CreateUserDTO): Promise<UserResponseDTO> {
    // 检查用户名是否已存在
    const existingUser = await User.findOne({
      where: { username: createUserDto.username },
    });

    if (existingUser) {
      throw new Error('用户名已存在');
    }

    // 检查邮箱是否已存在
    if (createUserDto.email) {
      const existingEmail = await User.findOne({
        where: { email: createUserDto.email },
      });

      if (existingEmail) {
        throw new Error('邮箱已存在');
      }
    }

    // 创建用户
    const user = await User.create({
      username: createUserDto.username,
      password: createUserDto.password, // 密码会在实体中自动加密
      nickname: createUserDto.nickname,
      email: createUserDto.email,
      avatar: createUserDto.avatar,
      role: createUserDto.role || 'user',
      isActive: createUserDto.isActive !== false,
    });

    return new UserResponseDTO(user);
  }

  /**
   * 更新用户
   */
  async updateUser(
    id: number,
    updateUserDto: UpdateUserDTO
  ): Promise<UserResponseDTO> {
    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      throw new Error('用户不存在');
    }

    // 检查用户名是否已被其他用户使用
    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUser = await User.findOne({
        where: {
          username: updateUserDto.username,
          id: { [Op.ne]: id },
        },
      });

      if (existingUser) {
        throw new Error('用户名已存在');
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingEmail = await User.findOne({
        where: {
          email: updateUserDto.email,
          id: { [Op.ne]: id },
        },
      });

      if (existingEmail) {
        throw new Error('邮箱已存在');
      }
    }

    // 更新用户信息
    const updateData: any = {};
    if (updateUserDto.username) updateData.username = updateUserDto.username;
    if (updateUserDto.password) updateData.password = updateUserDto.password; // 密码会在实体中自动加密
    if (updateUserDto.nickname !== undefined)
      updateData.nickname = updateUserDto.nickname;
    if (updateUserDto.email !== undefined) updateData.email = updateUserDto.email;
    if (updateUserDto.avatar !== undefined) updateData.avatar = updateUserDto.avatar;
    if (updateUserDto.role) updateData.role = updateUserDto.role;
    if (updateUserDto.isActive !== undefined)
      updateData.isActive = updateUserDto.isActive;

    await user.update(updateData);
    await user.reload();

    return new UserResponseDTO(user);
  }

  /**
   * 删除用户
   */
  async deleteUser(id: number): Promise<void> {
    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      throw new Error('用户不存在');
    }

    // 不允许删除管理员用户
    if (user.role === 'admin') {
      throw new Error('不能删除管理员用户');
    }

    // 删除用户
    await user.destroy();
  }

  /**
   * 获取用户详情
   */
  async getUserById(id: number): Promise<UserResponseDTO> {
    const user = await User.findByPk(id);
    if (!user) {
      throw new Error('用户不存在');
    }
    return new UserResponseDTO(user);
  }

  /**
   * 分页获取用户列表
   */
  async getUserList(
    queryDto: UserQueryDTO
  ): Promise<PageResponseDTO<UserResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, role, isActive } = queryDto;

    // 构建查询条件
    const where: any = {};

    if (keyword) {
      where[Op.or] = [
        { username: { [Op.like]: `%${keyword}%` } },
        { nickname: { [Op.like]: `%${keyword}%` } },
        { email: { [Op.like]: `%${keyword}%` } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    // 查询用户列表
    const { rows: users, count: total } = await User.findAndCountAll({
      where,
      limit: pageSize,
      offset: (page - 1) * pageSize,
      order: [['createdAt', 'DESC']],
    });

    const userDtos = users.map(user => new UserResponseDTO(user));
    return new PageResponseDTO(userDtos, total, page, pageSize);
  }

  /**
   * 验证用户权限
   */
  async validateUser(userId: number): Promise<any> {
    const user = await User.findByPk(userId);
    if (!user || !user.isActive) {
      throw new Error('用户不存在或已被禁用');
    }

    return {
      id: user.id,
      username: user.username,
      role: user.role,
      isActive: user.isActive,
    };
  }

  /**
   * 修改密码
   */
  async changePassword(
    userId: number,
    oldPassword: string,
    newPassword: string
  ): Promise<void> {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      throw new Error('原密码错误');
    }

    // 更新密码
    await user.update({ password: newPassword }); // 密码会在实体中自动加密
  }

  /**
   * 重置密码
   */
  async resetPassword(userId: number, newPassword: string): Promise<void> {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    // 更新密码
    await user.update({ password: newPassword }); // 密码会在实体中自动加密
  }
}
