import { Catch, Context } from '@midwayjs/core';

@Catch()
export class DefaultErrorFilter {
  async catch(err: Error, ctx: Context) {
    // 所有的未分类错误会到这里
    console.log('🚨 未分类错误：', {
      message: err.message,
      stack: err.stack,
      name: err.name,
      path: ctx.path,
      method: ctx.method,
      query: ctx.query,
      body: ctx.request.body,
    });
    ctx.logger.error(err);
    return {
      errCode: 500,
      msg: err.message || '内部错误，请联系管理员',
    };
  }
}
