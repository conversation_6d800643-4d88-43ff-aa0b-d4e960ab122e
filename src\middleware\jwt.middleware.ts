import { Inject, Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';
import { JwtService } from '@midwayjs/jwt';
import { UserService } from '../service/user.service';

@Middleware()
export class JwtMiddleware {
  @Inject()
  jwtService: JwtService;

  @Inject()
  userService: UserService;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      console.log('🔑 JWT中间件开始处理:', {
        path: ctx.path,
        method: ctx.method,
      });

      // 跳过不需要认证的路径
      const skipPaths = [
        '/public/map/data',
        '/public/map/detail',
        '/public/map/statistics',
        '/public/data',
        '/admin/auth/login',
        '/public',
      ];

      const shouldSkip = skipPaths.some(path => ctx.path.startsWith(path));
      if (shouldSkip) {
        console.log('🔑 跳过JWT验证的路径');
        await next();
        return;
      }

      // 获取token
      const token = this.extractTokenFromHeader(ctx);
      if (!token) {
        console.log('🔑 未提供认证令牌');
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '未提供认证令牌',
          code: 401,
        };
        return;
      }

      try {
        // 验证token
        const payload = (await this.jwtService.verify(token)) as any;
        console.log('🔑 Token验证成功:', { userId: payload.userId });

        // 验证用户是否存在且启用
        const user = await this.userService.validateUser(payload.userId);
        console.log('🔑 用户验证成功:', user);

        // 将用户信息添加到上下文
        ctx.state.user = {
          id: user.id,
          username: user.username,
          role: user.role,
        };

        console.log('🔑 JWT验证通过，继续执行');
        await next();
      } catch (error) {
        console.error('🔑 Token验证失败:', error);
        ctx.throw(401, '认证令牌无效或已过期');
      }
    };
  }

  private extractTokenFromHeader(ctx: Context): string | null {
    const authorization = ctx.headers.authorization;
    if (!authorization) {
      return null;
    }

    const parts = authorization.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }
}
