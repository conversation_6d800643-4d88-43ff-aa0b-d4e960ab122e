import { Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';

@Middleware()
export class AuthMiddleware {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 检查是否是管理端接口
      if (!ctx.path.startsWith('/admin/')) {
        await next();
        return;
      }

      // 跳过登录接口
      if (ctx.path === '/admin/auth/login') {
        await next();
        return;
      }

      // 检查用户是否已认证
      if (!ctx.state.user) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未认证',
          code: 401,
        };
        return;
      }

      // 检查用户角色权限
      const user = ctx.state.user;
      if (user.role !== 'admin') {
        ctx.status = 403;
        ctx.body = {
          success: false,
          message: '权限不足，需要管理员权限',
          code: 403,
        };
        return;
      }

      await next();
    };
  }
}
