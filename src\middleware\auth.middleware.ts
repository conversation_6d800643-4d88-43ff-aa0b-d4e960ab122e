import { Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';

@Middleware()
export class AuthMiddleware {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      console.log('🔐 Auth中间件开始处理:', {
        path: ctx.path,
        method: ctx.method,
        query: ctx.query,
        body: ctx.request.body,
      });

      // 检查是否是管理端接口
      if (!ctx.path.startsWith('/admin/')) {
        console.log('🔐 非管理端接口，跳过权限检查');
        await next();
        return;
      }

      // 跳过登录接口
      if (ctx.path === '/admin/auth/login') {
        console.log('🔐 登录接口，跳过权限检查');
        await next();
        return;
      }

      // 检查用户是否已认证
      if (!ctx.state.user) {
        console.log('🔐 用户未认证');
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未认证',
          code: 401,
        };
        return;
      }

      // 检查用户角色权限
      const user = ctx.state.user;
      console.log('🔐 检查用户权限:', user);

      if (user.role !== 'admin') {
        console.log('🔐 权限不足，用户角色:', user.role);
        ctx.status = 403;
        ctx.body = {
          success: false,
          message: '权限不足，需要管理员权限',
          code: 403,
        };
        return;
      }

      console.log('🔐 权限验证通过，继续执行');
      await next();
    };
  }
}
