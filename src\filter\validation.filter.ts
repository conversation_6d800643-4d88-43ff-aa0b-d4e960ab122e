import { Catch, MidwayValidationError } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';

@Catch(MidwayValidationError)
export class ValidationFilter {
  async catch(err: MidwayValidationError, ctx: Context) {
    console.log('🔍 验证错误过滤器捕获到错误:', {
      message: err.message,
      stack: err.stack,
      name: err.name,
      path: ctx.path,
      method: ctx.method,
      query: ctx.query,
      body: ctx.request.body,
      validationErrors: err.errors || [],
    });

    ctx.logger.error('验证错误:', err);
    
    return {
      errCode: 400,
      msg: err.message || '参数验证失败',
      errors: err.errors || [],
    };
  }
}
