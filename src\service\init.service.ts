import { Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { RegionDict } from '../entity/region-dict.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { User } from '../entity/user.entity';
// import * as bcrypt from 'bcryptjs';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class InitService {
  /**
   * 初始化数据库数据
   */
  async initializeData() {
    // 检查是否已经初始化过
    const regionCount = await RegionDict.count();
    if (regionCount > 0) {
      console.log('📋 数据库已存在数据，跳过初始化');
      return;
    }

    console.log('📋 开始初始化数据库数据...');

    // 初始化区域字典
    await this.initRegionDict();

    // 初始化类型字典
    await this.initTypeDict();

    // 初始化关系字典
    await this.initRelationshipDict();

    // 初始化默认用户
    await this.initDefaultUser();

    console.log('📋 数据库数据初始化完成');
  }

  /**
   * 初始化区域字典
   */
  private async initRegionDict() {
    const regionData = [
      {
        regionCode: 'REGION_XIAN',
        regionName: '西安',
        parentId: 1,
        status: 1,
        sort: 1,
        regionDesc: '西安市及其周边区域',
      },
    ];

    await RegionDict.bulkCreate(regionData);
    console.log('  ✓ 区域字典初始化完成');
  }

  /**
   * 初始化类型字典
   */
  private async initTypeDict() {
    const typeData = [];

    await TypeDict.bulkCreate(typeData);
    console.log('  ✓ 类型字典初始化完成');
  }

  /**
   * 初始化关系字典
   */
  private async initRelationshipDict() {
    const relationData = [
      {
        relationCode: 'RELATION_LOCATION',
        relationName: '选址关联',
        parentId: null,
        status: 1,
        sort: 1,
        relationDesc: '山塬与城镇、建筑的选址依赖关系',
      },
      {
        relationCode: 'RELATION_VISUAL',
        relationName: '视线关联',
        parentId: null,
        status: 1,
        sort: 2,
        relationDesc: '不同要素间的视觉联系关系',
      },
      {
        relationCode: 'RELATION_WATER',
        relationName: '水系滋养',
        parentId: null,
        status: 1,
        sort: 3,
        relationDesc: '水系对周边地理要素的滋养关系',
      },
      {
        relationCode: 'RELATION_HISTORY',
        relationName: '历史沿革',
        parentId: null,
        status: 1,
        sort: 4,
        relationDesc: '历史要素间的时间演变关系',
      },
    ];

    await RelationshipDict.bulkCreate(relationData);
    console.log('  ✓ 关系字典初始化完成');
  }

  /**
   * 初始化默认用户
   */
  private async initDefaultUser() {
    // 检查是否已存在管理员用户
    const existingUser = await User.findOne({
      where: { username: 'admin' } as any,
    });

    if (existingUser) {
      console.log('  ✓ 默认管理员用户已存在');
      return;
    }

    // 创建默认管理员用户
    // const hashedPassword = await bcrypt.hash('admin123', 10);
    await User.create({
      username: 'admin',
      password: 'admin123',
      role: 'admin',
    } as any);

    console.log('  ✓ 默认管理员用户创建完成 (用户名: admin, 密码: admin123)');
  }
}
