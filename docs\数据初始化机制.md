# 数据初始化机制

## 概述

智慧营建系统的数据初始化机制已经升级，现在能够检测并自动修复核心初始数据丢失的问题，包括admin用户被误删等情况。

## 新特性

### 1. 分层检查机制

原来的初始化逻辑只检查区域表是否有数据，现在改为分别检查各个核心数据表：

- **区域字典数据** - 检查 `region_dict` 表
- **类型字典数据** - 检查 `type_dict` 表  
- **关系字典数据** - 检查 `relationship_dict` 表
- **管理员用户** - 检查 `user` 表中的admin用户

### 2. 智能修复功能

系统现在能够检测并自动修复以下问题：

- **admin用户缺失** - 自动创建默认管理员用户
- **admin用户角色异常** - 自动修复为admin角色
- **admin用户被禁用** - 自动启用用户
- **基础字典数据缺失** - 自动初始化缺失的字典数据

### 3. API接口支持

提供了管理API接口用于手动验证和修复：

```http
# 验证核心数据完整性（只检查不修复）
GET /api/admin/system/data/validate

# 修复核心数据问题（检查并修复）
POST /api/admin/system/data/repair

# 重新初始化所有数据
POST /api/admin/system/data/reinitialize
```

## 使用场景

### 场景1：admin用户被误删

当admin用户被误删时，系统会在下次启动时自动检测并重新创建：

```
📋 检测到管理员用户缺失，开始创建...
✓ 默认管理员用户创建完成 (用户名: admin, 密码: admin123)
```

### 场景2：admin用户角色被修改

如果admin用户的角色被意外修改，系统会自动修复：

```
📋 检测到管理员用户角色异常，正在修复...
✓ 管理员用户角色已修复
```

### 场景3：admin用户被禁用

如果admin用户被禁用，系统会自动启用：

```
📋 检测到管理员用户被禁用，正在启用...
✓ 管理员用户已启用
```

### 场景4：手动验证和修复

通过API接口手动检查和修复数据问题：

```bash
# 检查数据完整性
curl -X GET http://localhost:7001/api/admin/system/data/validate

# 修复发现的问题
curl -X POST http://localhost:7001/api/admin/system/data/repair
```

## 技术实现

### 核心方法

1. **initializeData()** - 主初始化方法，分别检查各个数据表
2. **validateCoreData()** - 验证核心数据完整性，返回详细的检查和修复结果
3. **checkAndInitDefaultUser()** - 专门检查和修复admin用户相关问题

### 启动时机

- 应用启动时自动执行（通过 `AutoloadListener`）
- 只在主进程中执行（避免多进程重复初始化）
- 支持通过API手动触发

### 日志输出

系统会输出详细的检查和修复日志：

```
📋 开始检查并初始化数据库数据...
📋 区域字典数据正常
📋 类型字典数据正常  
📋 关系字典数据正常
📋 检测到管理员用户缺失，开始创建...
  ✓ 默认管理员用户创建完成 (用户名: admin, 密码: admin123)
📋 数据库数据检查和初始化完成
```

## 测试

可以使用提供的测试脚本验证功能：

```bash
node scripts/test-init-service.js
```

## 注意事项

1. **密码安全** - 默认admin密码为 `admin123`，生产环境中应及时修改
2. **权限控制** - 修复API需要管理员权限
3. **数据备份** - 在进行数据修复前建议备份数据库
4. **日志监控** - 关注启动日志，及时发现数据问题

## 配置

默认的admin用户配置：

```javascript
{
  username: 'admin',
  password: 'admin123',
  role: 'admin',
  nickname: '系统管理员',
  isActive: true
}
```

如需修改默认配置，请编辑 `src/service/init.service.ts` 中的 `initDefaultUser` 方法。
