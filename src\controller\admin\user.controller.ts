import { Controller, Post, Get, Put, Del, Body, Param, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { UserService } from '../../service/user.service';
import {
  CreateUserDTO,
  UpdateUserDTO,
  UserQueryDTO,
  ResetPasswordDTO,
} from '../../dto/user.dto';

/**
 * 用户管理控制器
 */
@Controller('/admin/user', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminUserController {
  @Inject()
  userService: UserService;

  /**
   * 创建用户
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateUserDTO) {
    const data = await this.userService.createUser(createDto);
    return data;
  }

  /**
   * 更新用户
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdateUserDTO) {
    const data = await this.userService.updateUser(id, updateDto);
    return data;
  }

  /**
   * 删除用户
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    await this.userService.deleteUser(id);
    return { message: '删除成功' };
  }

  /**
   * 获取用户详情
   */
  @Get('/:id')
  async getById(@Param('id') id: number) {
    const data = await this.userService.getUserById(id);
    return data;
  }

  /**
   * 获取用户列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() queryDto: UserQueryDTO) {
    console.log('👥 用户列表查询开始:', {
      queryDto,
      rawQuery: queryDto,
    });

    try {
      const data = await this.userService.getUserList(queryDto);
      console.log('👥 用户列表查询成功:', {
        total: data.total,
        page: data.page,
        pageSize: data.pageSize,
      });
      return data;
    } catch (error) {
      console.error('👥 用户列表查询失败:', error);
      throw error;
    }
  }

  /**
   * 重置用户密码
   */
  @Post('/:id/reset-password')
  @Validate()
  async resetPassword(
    @Param('id') id: number,
    @Body() resetDto: ResetPasswordDTO
  ) {
    await this.userService.resetPassword(id, resetDto.newPassword);
    return { message: '密码重置成功' };
  }

  /**
   * 启用/禁用用户
   */
  @Post('/:id/toggle-status')
  async toggleStatus(@Param('id') id: number) {
    const user = await this.userService.getUserById(id);
    await this.userService.updateUser(id, { isActive: !user.isActive });
    return { 
      message: user.isActive ? '用户已禁用' : '用户已启用',
      isActive: !user.isActive
    };
  }
}
